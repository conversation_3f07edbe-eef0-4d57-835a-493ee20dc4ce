#!/usr/bin/env python3
"""
Manual test commands for async functionality.
This script provides simple commands to test async endpoints manually.
"""

import requests
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed


BASE_URL = "http://localhost:8769"


def make_request(endpoint, request_id=None):
    """Make a single request and print the result."""
    url = f"{BASE_URL}{endpoint}"
    start_time = time.time()
    
    try:
        response = requests.get(url)
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Request {request_id or ''} to {endpoint}:")
        print(f"  Status: {response.status_code}")
        print(f"  Duration: {duration:.2f}s")
        print(f"  Response: {response.json()}")
        print()
        
        return {
            "endpoint": endpoint,
            "status": response.status_code,
            "duration": duration,
            "response": response.json()
        }
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"Request {request_id or ''} to {endpoint} FAILED:")
        print(f"  Error: {e}")
        print(f"  Duration: {duration:.2f}s")
        print()
        return None


def test_basic_async():
    """Test basic async functionality."""
    print("🧪 Testing Basic Async Functionality")
    print("This will start a long sleep (12s) and then call quick endpoints")
    print("The quick endpoints should respond immediately\n")
    
    # Use ThreadPoolExecutor to simulate concurrent requests
    with ThreadPoolExecutor(max_workers=5) as executor:
        # Submit all requests at roughly the same time
        futures = []
        
        # Long sleep request
        futures.append(executor.submit(make_request, "/async-test/long-sleep", "LONG"))
        
        # Quick requests
        for i in range(3):
            futures.append(executor.submit(make_request, "/async-test/quick", f"QUICK-{i+1}"))
        
        # Wait for all to complete
        for future in as_completed(futures):
            result = future.result()


def test_multiple_durations():
    """Test multiple sleep durations."""
    print("🧪 Testing Multiple Sleep Durations")
    print("Starting very long sleep (25s) and medium sleep (10s)")
    print("Medium sleep should complete before very long sleep\n")
    
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        
        # Very long sleep
        futures.append(executor.submit(make_request, "/async-test/very-long-sleep", "VERY-LONG"))
        
        # Medium sleep (start slightly after)
        time.sleep(1)
        futures.append(executor.submit(make_request, "/async-test/medium-sleep", "MEDIUM-1"))
        futures.append(executor.submit(make_request, "/async-test/medium-sleep", "MEDIUM-2"))
        
        # Wait for all to complete
        for future in as_completed(futures):
            result = future.result()


def test_load(num_requests=10):
    """Test load with multiple concurrent requests."""
    print(f"🧪 Testing Load ({num_requests} concurrent requests)")
    print("Sending multiple requests to load test endpoint\n")
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=num_requests) as executor:
        futures = [
            executor.submit(make_request, "/async-test/load-test", f"LOAD-{i+1}")
            for i in range(num_requests)
        ]
        
        results = []
        for future in as_completed(futures):
            result = future.result()
            if result:
                results.append(result)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"Load test completed in {total_time:.2f}s")
    print(f"Successful requests: {len(results)}/{num_requests}")
    
    if results:
        durations = [r["duration"] for r in results]
        print(f"Average response time: {sum(durations)/len(durations):.2f}s")
        print(f"Max response time: {max(durations):.2f}s")
        print(f"Min response time: {min(durations):.2f}s")


def test_health():
    """Test health endpoint."""
    print("🧪 Testing Health Endpoint")
    make_request("/async-test/health", "HEALTH")


def test_custom_sleep(seconds=5):
    """Test custom sleep endpoint."""
    print(f"🧪 Testing Custom Sleep ({seconds}s)")
    make_request(f"/async-test/custom-sleep?sleep_seconds={seconds}", f"CUSTOM-{seconds}s")


def main():
    """Main menu for manual testing."""
    print("🚀 FastAPI Async Manual Testing")
    print("Make sure your server is running on http://localhost:8769\n")
    
    while True:
        print("Choose a test:")
        print("1. Health check")
        print("2. Basic async test (long sleep + quick responses)")
        print("3. Multiple duration test (25s + 10s sleeps)")
        print("4. Load test (10 concurrent requests)")
        print("5. Heavy load test (50 concurrent requests)")
        print("6. Custom sleep test (5 seconds)")
        print("7. Quick response test")
        print("0. Exit")
        
        choice = input("\nEnter your choice (0-7): ").strip()
        
        if choice == "0":
            print("Goodbye!")
            break
        elif choice == "1":
            test_health()
        elif choice == "2":
            test_basic_async()
        elif choice == "3":
            test_multiple_durations()
        elif choice == "4":
            test_load(10)
        elif choice == "5":
            test_load(50)
        elif choice == "6":
            test_custom_sleep(5)
        elif choice == "7":
            make_request("/async-test/quick", "QUICK")
        else:
            print("Invalid choice. Please try again.")
        
        print("\n" + "="*50 + "\n")


if __name__ == "__main__":
    main()
