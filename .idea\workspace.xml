<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="da829768-5df5-4017-b7ed-9b43f1bd6311" name="Changes" comment="fix: rename everything">
      <change afterPath="$PROJECT_DIR$/app/context/dependencies.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/context/request_data.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/modules/hull_performance/logic_helpers/hull_performance_helper.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/modules/mrv/logic_helpers/mrv_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/config/requirements.txt" beforeDir="false" afterPath="$PROJECT_DIR$/app/config/requirements.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/cii.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/cii/logic_helpers/cii.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/cii_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/cii/logic_helpers/cii_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/configuration_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/config/configuration_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/data_analytics_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/helpers/data/data_analytics_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/data_controller.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/controllers/data_controller.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/data_handler.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/handlers/data_handler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/donut_chart_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/helpers/charts/donut_chart_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/efficiency_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/efficiency/logic_helpers/efficiency_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/excel_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/helpers/excel/excel_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/generate_file.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/export/logic_helpers/generate_file.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/interchange_formats.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/utils/interchange_formats.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/measurements.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/handlers/measurements.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/monthly_fuel_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/helpers/data/monthly_fuel_data.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/pie_chart_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/helpers/charts/pie_chart_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/special_vessel_keys.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/utils/special_vessel_keys.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/general_logic/helpers/summary_helper.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/general_logic_helper/helpers/data/summary_helper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/anomaly_detection/anomaly_detection_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/anomaly_detection/anomaly_detection_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/anomaly_detection/anomaly_detection_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/anomaly_detection/anomaly_detection_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/cii/cii_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/cii/cii_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/cii/cii_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/cii/cii_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/cii/cii_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/cii/cii_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/data_analytics/data_analytics_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/data_analytics/data_analytics_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/data_analytics/data_analytics_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/data_analytics/data_analytics_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/efficiency/efficiency_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/efficiency/efficiency_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/efficiency/efficiency_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/efficiency/efficiency_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/engine_health/engine_health_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/engine_health/engine_health_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/engine_health/engine_health_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/engine_health/engine_health_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/export/export_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/export/export_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/export/export_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/export/export_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/hull_performance/hull_performance_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/hull_performance/hull_performance_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/hull_performance/hull_performance_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/hull_performance/hull_performance_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/mrv/mrv_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/mrv/mrv_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/mrv/mrv_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/mrv/mrv_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/owner/owner_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/owner/owner_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/owner/owner_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/owner/owner_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/user/user_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/user/user_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/user/user_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/user/user_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/vessel/vessel_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/vessel/vessel_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/modules/vessel/vessel_router.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/modules/vessel/vessel_router.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/services/redis_api/redis_api_client.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/services/redis_api/redis_api_client.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/services/redis_api/redis_api_client_base.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/services/redis_api/redis_api_client_base.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/services/redis_api/redis_api_client_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/services/redis_api/redis_api_client_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/services/redis_api/redis_api_client_error.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/services/redis_api/redis_api_client_error.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/utils/dependencies.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/context/context.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/utils/errors.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/utils/errors.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/utils/factory.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/utils/request_data.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app/utils/utils.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/context/utils.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/run.py" beforeDir="false" afterPath="$PROJECT_DIR$/run.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/settings.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/config/settings.py" afterDir="false" />
    </list>
    <list id="4c8177e0-3a1d-4393-833d-45a979f323da" name="local" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/backend-next.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/Project_Default.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/profiles_settings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zdBn0aeHU7KItFFjVbQTCCu1Hb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.router.executor": "Run",
    "Python.run fastapi uvicorn.executor": "Run",
    "Python.run.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "da/refactoring",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/projects/redis-api",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\modules\export" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic\helpers" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\vessel" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\config" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\config" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic_helper\utils" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic_helper\config" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic_helper\helpers\excel" />
      <recent name="C:\Users\<USER>\Desktop\projects\backend-next\app\general_logic_helper\helpers\data" />
    </key>
  </component>
  <component name="RunManager" selected="Python.run">
    <configuration name="run fastapi uvicorn" type="PythonConfigurationType" factoryName="Python">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Python 3.11 (backend-next)" />
      <option name="WORKING_DIRECTORY" value="C:\Users\<USER>\Desktop\projects\backend-next" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="uvicorn" />
      <option name="PARAMETERS" value="run:app --host 0.0.0.0 --port 8769 --reload" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="true" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="run" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="backend-next" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Python 3.11 (backend-next)" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/run.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.run fastapi uvicorn" />
      <item itemvalue="Python.run" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19416.19" />
        <option value="bundled-python-sdk-337b0a7a993a-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19416.19" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="da829768-5df5-4017-b7ed-9b43f1bd6311" name="Changes" comment="" />
      <changelist id="4c8177e0-3a1d-4393-833d-45a979f323da" name="local" comment="" />
      <created>1752051232428</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752051232428</updated>
      <workItem from="1752051233476" duration="14834000" />
      <workItem from="1752128789726" duration="22998000" />
      <workItem from="1752215157284" duration="14887000" />
      <workItem from="1752476439586" duration="18220000" />
      <workItem from="1752560617911" duration="22636000" />
      <workItem from="1752735317003" duration="16919000" />
      <workItem from="1752825168982" duration="9208000" />
      <workItem from="1753079438382" duration="20419000" />
      <workItem from="1753165146807" duration="23548000" />
      <workItem from="1753255542875" duration="21607000" />
      <workItem from="1753337543803" duration="31385000" />
      <workItem from="1753383556117" duration="26098000" />
    </task>
    <task id="LOCAL-00001" summary="feat: initial commit, first structure idea">
      <option name="closed" value="true" />
      <created>1752582389566</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752582389566</updated>
    </task>
    <task id="LOCAL-00002" summary="fix: make generic dependency handler">
      <option name="closed" value="true" />
      <created>1752742324705</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752742324705</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: add owner vat check, minor fixes">
      <option name="closed" value="true" />
      <created>1752745602745</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752745602745</updated>
    </task>
    <task id="LOCAL-00004" summary="fix: move files around, fixed hull p. endpoint names and vat check, Hull service is not singleton anymore, owner vat check specified for each endpoint not router, token check">
      <option name="closed" value="true" />
      <created>1752835407781</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752835407781</updated>
    </task>
    <task id="LOCAL-00005" summary="feat: added data parameter to call method">
      <option name="closed" value="true" />
      <created>1753104288925</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753104288925</updated>
    </task>
    <task id="LOCAL-00006" summary="fix: change structure and naming of route dependencies">
      <option name="closed" value="true" />
      <created>1753104346468</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753104346468</updated>
    </task>
    <task id="LOCAL-00007" summary="feat: added general redis calls to redis api client">
      <option name="closed" value="true" />
      <created>1753104393279</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753104393279</updated>
    </task>
    <task id="LOCAL-00008" summary="feat: added helpers, for now in this structure, may change later on">
      <option name="closed" value="true" />
      <created>1753104549530</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753104549530</updated>
    </task>
    <task id="LOCAL-00009" summary="feat: anomaly detection module">
      <option name="closed" value="true" />
      <created>1753104611544</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753104611544</updated>
    </task>
    <task id="LOCAL-00010" summary="feat: user module">
      <option name="closed" value="true" />
      <created>1753104634711</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753104634711</updated>
    </task>
    <task id="LOCAL-00011" summary="feat: owner module">
      <option name="closed" value="true" />
      <created>1753104668730</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753104668730</updated>
    </task>
    <task id="LOCAL-00012" summary="feat: vessel module">
      <option name="closed" value="true" />
      <created>1753104684862</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753104684862</updated>
    </task>
    <task id="LOCAL-00013" summary="feat: home module">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00014" summary="feat: efficiency calculation module">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00015" summary="fix: update hull performance router to account for the change in dependencies">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00016" summary="feat: add new routers to fast api">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00017" summary="fix: moved file">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00018" summary="fix: fix base redis client json and data handling in request">
      <option name="closed" value="true" />
      <created>1753188825434</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753188825434</updated>
    </task>
    <task id="LOCAL-00019" summary="fix: Change call to redis client for calculation helpers">
      <option name="closed" value="true" />
      <created>1753188946850</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753188946850</updated>
    </task>
    <task id="LOCAL-00020" summary="fix: fix time series method to work with home page and data analytics">
      <option name="closed" value="true" />
      <created>1753188999835</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753188999835</updated>
    </task>
    <task id="LOCAL-00021" summary="feat: get dependencies without service class for calculation modules">
      <option name="closed" value="true" />
      <created>1753189056780</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753189056780</updated>
    </task>
    <task id="LOCAL-00022" summary="fix: minor changes and fixes in user module">
      <option name="closed" value="true" />
      <created>1753189153776</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753189153776</updated>
    </task>
    <task id="LOCAL-00023" summary="fix: move dependency in efficiency module">
      <option name="closed" value="true" />
      <created>1753189222959</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753189222959</updated>
    </task>
    <task id="LOCAL-00024" summary="fix: move dependency in vessel module">
      <option name="closed" value="true" />
      <created>1753189251866</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753189251866</updated>
    </task>
    <task id="LOCAL-00025" summary="fix: move dependency in home module">
      <option name="closed" value="true" />
      <created>1753189295783</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753189295783</updated>
    </task>
    <task id="LOCAL-00026" summary="feat: cii module">
      <option name="closed" value="true" />
      <created>1753189358199</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1753189358199</updated>
    </task>
    <task id="LOCAL-00027" summary="feat: mrv module">
      <option name="closed" value="true" />
      <created>1753189387290</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753189387290</updated>
    </task>
    <task id="LOCAL-00028" summary="feat: data analytics module">
      <option name="closed" value="true" />
      <created>1753189407141</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753189407141</updated>
    </task>
    <task id="LOCAL-00029" summary="feat: add routers to fastapi">
      <option name="closed" value="true" />
      <created>1753189446197</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753189446197</updated>
    </task>
    <task id="LOCAL-00030" summary="feat: new ways to get params">
      <option name="closed" value="true" />
      <created>1753276917408</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753276917408</updated>
    </task>
    <task id="LOCAL-00031" summary="feat: new ways to get params, modules update">
      <option name="closed" value="true" />
      <created>1753277059336</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753277059336</updated>
    </task>
    <task id="LOCAL-00032" summary="feat: export module">
      <option name="closed" value="true" />
      <created>1753277121608</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753277121608</updated>
    </task>
    <task id="LOCAL-00033" summary="feat: engine health module , we will test and use it later on">
      <option name="closed" value="true" />
      <created>1753277143796</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753277143796</updated>
    </task>
    <task id="LOCAL-00034" summary="fix: rename redis service to redis api">
      <option name="closed" value="true" />
      <created>1753278744509</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753278744509</updated>
    </task>
    <task id="LOCAL-00035" summary="feat: break up dependencies into logical units">
      <option name="closed" value="true" />
      <created>1753369552197</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1753369552197</updated>
    </task>
    <task id="LOCAL-00036" summary="feat: break up dependencies into logical units">
      <option name="closed" value="true" />
      <created>1753369564024</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753369564024</updated>
    </task>
    <task id="LOCAL-00037" summary="feat: use config for fastapi and uvicorn">
      <option name="closed" value="true" />
      <created>1753369637675</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753369637675</updated>
    </task>
    <task id="LOCAL-00038" summary="feat: config for redis client">
      <option name="closed" value="true" />
      <created>1753369672338</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1753369672338</updated>
    </task>
    <task id="LOCAL-00039" summary="fix: error handling improvements">
      <option name="closed" value="true" />
      <created>1753369698949</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1753369698949</updated>
    </task>
    <task id="LOCAL-00040" summary="fix: move file to general logic for now">
      <option name="closed" value="true" />
      <created>1753369712346</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1753369712346</updated>
    </task>
    <task id="LOCAL-00041" summary="feat: change router's and logic's parameter handling to use the general way of just passing on the parameters">
      <option name="closed" value="true" />
      <created>1753369811359</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1753369811359</updated>
    </task>
    <task id="LOCAL-00042" summary="fix: rename everything">
      <option name="closed" value="true" />
      <created>1753384811315</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1753384811315</updated>
    </task>
    <option name="localTasksCounter" value="43" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix: moved file" />
    <MESSAGE value="fix: fix base redis client json and data handling in request" />
    <MESSAGE value="fix: Change call to redis client for calculation helpers" />
    <MESSAGE value="fix: fix time series method to work with home page and data analytics" />
    <MESSAGE value="feat: get dependencies without service class for calculation modules" />
    <MESSAGE value="fix: minor changes and fixes in user module" />
    <MESSAGE value="fix: move dependency in efficiency module" />
    <MESSAGE value="fix: move dependency in vessel module" />
    <MESSAGE value="fix: move dependency in home module" />
    <MESSAGE value="feat: cii module" />
    <MESSAGE value="feat: mrv module" />
    <MESSAGE value="feat: data analytics module" />
    <MESSAGE value="feat: add routers to fastapi" />
    <MESSAGE value="feat: new ways to get params" />
    <MESSAGE value="feat: new ways to get params, modules update" />
    <MESSAGE value="feat: export module" />
    <MESSAGE value="feat: engine health module , we will test and use it later on" />
    <MESSAGE value="fix: rename redis service to redis api" />
    <MESSAGE value="feat: break up dependencies into logical units" />
    <MESSAGE value="feat: use config for fastapi and uvicorn" />
    <MESSAGE value="feat: config for redis client" />
    <MESSAGE value="fix: error handling improvements" />
    <MESSAGE value="fix: move file to general logic for now" />
    <MESSAGE value="feat: change router's and logic's parameter handling to use the general way of just passing on the parameters" />
    <MESSAGE value="fix: rename everything" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: rename everything" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/export/logic_helpers/generate_file.py</url>
          <line>280</line>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/efficiency/efficiency_logic.py</url>
          <line>11</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/efficiency/efficiency_router.py</url>
          <line>23</line>
          <option name="timeStamp" value="100" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/anomaly_detection/anomaly_detection_router.py</url>
          <line>20</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app/modules/owner/owner_router.py</url>
          <line>21</line>
          <option name="timeStamp" value="106" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/auth.py</url>
          <line>36</line>
          <option name="timeStamp" value="108" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/backend_next$run_fastapi_uvicorn.coverage" NAME="run fastapi uvicorn Coverage Results" MODIFIED="1753346193055" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="C:\Users\<USER>\Desktop\projects\backend-next" />
    <SUITE FILE_PATH="coverage/backend_next$router.coverage" NAME="router Coverage Results" MODIFIED="1753346545330" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/app/modules/mrv" />
    <SUITE FILE_PATH="coverage/backend_next$run.coverage" NAME="run Coverage Results" MODIFIED="1753442622790" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
  </component>
</project>