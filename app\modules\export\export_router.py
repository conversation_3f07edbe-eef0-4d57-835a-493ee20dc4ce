from fastapi import APIRouter, Depends, HTTPException
from auth import owner_vat_check

from app.modules.export.logic_helpers.generate_file import GenerateFile
from app.modules.export.export_logic import ExportLogic
from app.modules.export.export_service import ExportService
from app.context.context import get_context

router = APIRouter(prefix="/export", tags=["Export"])

get_export_params, get_export_logic = get_context(
    ExportLogic,
    ExportService,
    GenerateFile,
)


@router.get("/json", dependencies=[Depends(owner_vat_check)])
async def json(
    params: dict = Depends(get_export_params),
    logic: ExportLogic = Depends(get_export_logic),
):
    return await logic.json(**params)


@router.get("/excel", dependencies=[Depends(owner_vat_check)])
async def excel(
    params: dict = Depends(get_export_params),
    logic: ExportLogic = Depends(get_export_logic),
):
    return await logic.excel(**params)


@router.get("/cylinder/to", dependencies=[Depends(owner_vat_check)])
async def cylinder_to(
    params: dict = Depends(get_export_params),
    logic: ExportLogic = Depends(get_export_logic),
):
    return await logic.cylinder_to(**params)


@router.get("/cylinder/from", dependencies=[Depends(owner_vat_check)])
async def cylinder_from(
    params: dict = Depends(get_export_params),
    logic: ExportLogic = Depends(get_export_logic),
):
    return await logic.cylinder_from(**params)


@router.get("/cylinder", dependencies=[Depends(owner_vat_check)])
async def cylinder(
    params: dict = Depends(get_export_params),
    logic: ExportLogic = Depends(get_export_logic),
):
    result = await logic.cylinder(**params)
    if result == "Error":
        raise HTTPException(status_code=400, detail="No data provided")
    return result
