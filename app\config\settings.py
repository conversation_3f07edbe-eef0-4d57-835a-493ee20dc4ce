import os
from dotenv import load_dotenv

# Load environment variables from a .env file (if present)
load_dotenv()


class Settings:
    """
    Application settings loaded from environment variables.
    """
    def __init__(self):
        # JWT secret for authenticating users
        self.jwt_secret = os.getenv("JWT_SECRET")
        if not self.jwt_secret:
            raise RuntimeError("Missing required environment variable: JWT_SECRET")

        # Uvicorn server settings
        self.host = os.getenv("API_HOST", "0.0.0.0")
        self.port = int(os.getenv("API_PORT", "8769"))

        # Whether to auto-reload the server during development
        self.reload = os.getenv("API_RELOAD", "true").lower() in ("true", "1", "yes")
        self.log_level = "info"
        self.access_log = True


# Instantiate a single settings object for the entire app
settings = Settings()
