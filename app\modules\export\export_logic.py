from app.modules.export.logic_helpers.generate_file import GenerateFile
from app.modules.export.export_service import ExportService
from app.context.utils import rename_vat


class ExportLogic:
    def __init__(self, export_service: ExportService, generate_file: GenerateFile):
        self.export_service = export_service
        self.generate_file = generate_file

    async def json(
        self,
        vessel_imo: str,
        owner_vat: str,
        from_date: str,
        to_date: str,
    ):
        return await self.generate_file.generate_json(rename_vat(**locals()))

    async def excel(
        self,
        vessel_imo: str,
        owner_vat: str,
        from_date: str,
        to_date: str,
        vessel_id: int,
        vessel_name: str,
        sorted_type: bool,
        exclude_anomalies: bool,
    ):
        return await self.generate_file.generate_excel(rename_vat(**locals()))

    async def cylinder_to(self, vessel_imo: str, owner_vat: str):
        return await self.export_service.cylinder_to(rename_vat(**locals())) or []

    async def cylinder_from(self, vessel_imo: str, owner_vat: str):
        return await self.export_service.cylinder_from(rename_vat(**locals())) or []

    async def cylinder(self, vessel_imo: str, owner_vat: str, from_date: str, to_date: str):
        return await self.generate_file.generate_cylinder_json(rename_vat(**locals()))

