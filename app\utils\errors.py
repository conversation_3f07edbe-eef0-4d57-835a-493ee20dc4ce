import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
# Import Starlette's HTTPException to handle not found errors separately
from starlette.exceptions import HTTPException as StarletteHTTPException


def register_error_handlers(app: FastAPI) -> None:
    """
    Register global exception handlers for the FastAPI application.
    
    This function sets up consistent error handling and logging across the application.
    Each handler:
    - Catches a specific type of exception
    - Logs the error with relevant context
    - Returns a standardized JSON response
    
    Args:
        app (FastAPI): The FastAPI application instance
    """

    @app.exception_handler(StarletteHTTPException)
    async def not_found_handler(request: Request, exc: StarletteHTTPException):
        """
        Handle 404 Not Found and other Starlette HTTP exceptions.
        Logs the request method, path and error details.
        """
        logging.warning(
            "404 Not Found: %s %s → %d %s",
            request.method,
            request.url.path,
            exc.status_code,
            exc.detail or "Not Found",
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "status": "error",
                "message": exc.detail or "Not Found",
                "data": None,
            },
        )

    @app.exception_handler(HTTPException)
    async def http_exc_handler(request: Request, exc: HTTPException):
        """
        Handle FastAPI's HTTPException.
        Used for explicit error responses raised in route handlers.
        """
        logging.warning(
            "HTTPException: %s %s → %d %s",
            request.method,
            request.url.path,
            exc.status_code,
            exc.detail,
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "status": "error",
                "message": exc.detail,
                "data": None,
            },
        )

    @app.exception_handler(RequestValidationError) 
    async def validation_exc_handler(request: Request, exc: RequestValidationError):
        """
        Handle request validation errors (e.g. invalid data types, missing fields).
        Logs validation errors with detailed error list.
        """
        logging.info(
            "Validation error on %s %s: %s",
            request.method,
            request.url.path,
            exc.errors(),
        )
        return JSONResponse(
            status_code=422,
            content={
                "status": "error",
                "message": "Validation error",
                "errors": exc.errors(), # Include detailed validation errors
                "data": None,
            },
        )

    @app.exception_handler(Exception)
    async def generic_exc_handler(request: Request, exc: Exception):
        """
        Catch-all handler for any unhandled exceptions.
        Logs full traceback for debugging while returning a generic error to the client.
        """
        logging.exception(
            "Unhandled exception on %s %s: %s",
            request.method,
            request.url.path,
            str(exc),
        )
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": "Internal server error", # Generic message for security
                "data": None,
            },
        )
