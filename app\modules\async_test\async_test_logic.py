import asyncio
import random
import time
from typing import Dict, Any


class AsyncTestLogic:
    """
    Logic class for async testing endpoints.
    Contains methods to test various async scenarios.
    """

    async def quick_response(self, **params) -> Dict[str, Any]:
        """
        Returns immediate response with random data.
        This should not be blocked by other long-running endpoints.
        """
        return {
            "message": "Quick response",
            "random_number": random.randint(1, 1000),
            "timestamp": time.time(),
            "response_time_ms": 0
        }

    async def long_sleep(self, **params) -> Dict[str, Any]:
        """
        Sleeps for more than 10 seconds before returning data.
        This should not block other endpoints.
        """
        start_time = time.time()
        sleep_duration = 12  # 12 seconds
        
        await asyncio.sleep(sleep_duration)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        return {
            "message": "Long sleep completed",
            "requested_sleep_seconds": sleep_duration,
            "actual_duration_seconds": round(actual_duration, 2),
            "random_number": random.randint(1, 1000),
            "timestamp": end_time
        }

    async def medium_sleep(self, **params) -> Dict[str, Any]:
        """
        Sleeps for 10 seconds before returning data.
        Used for testing concurrent requests with different durations.
        """
        start_time = time.time()
        sleep_duration = 10  # 10 seconds
        
        await asyncio.sleep(sleep_duration)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        return {
            "message": "Medium sleep completed",
            "requested_sleep_seconds": sleep_duration,
            "actual_duration_seconds": round(actual_duration, 2),
            "random_number": random.randint(1, 1000),
            "timestamp": end_time
        }

    async def very_long_sleep(self, **params) -> Dict[str, Any]:
        """
        Sleeps for 25 seconds before returning data.
        Used for testing concurrent requests with very different durations.
        """
        start_time = time.time()
        sleep_duration = 25  # 25 seconds
        
        await asyncio.sleep(sleep_duration)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        return {
            "message": "Very long sleep completed",
            "requested_sleep_seconds": sleep_duration,
            "actual_duration_seconds": round(actual_duration, 2),
            "random_number": random.randint(1, 1000),
            "timestamp": end_time
        }

    async def load_test_endpoint(self, **params) -> Dict[str, Any]:
        """
        Endpoint designed for load testing.
        Simulates some work but returns quickly.
        """
        start_time = time.time()
        
        # Simulate some CPU work
        result = 0
        for i in range(100000):
            result += i * random.random()
        
        # Small async delay to simulate I/O
        await asyncio.sleep(0.1)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        return {
            "message": "Load test endpoint",
            "processing_time_seconds": round(processing_time, 4),
            "computation_result": round(result, 2),
            "random_number": random.randint(1, 1000),
            "timestamp": end_time,
            "request_id": random.randint(10000, 99999)
        }

    async def custom_sleep(self, sleep_seconds: int = 5, **params) -> Dict[str, Any]:
        """
        Sleeps for a custom duration specified in the request.
        Useful for testing specific timing scenarios.
        """
        start_time = time.time()
        
        # Ensure sleep duration is reasonable (max 60 seconds)
        sleep_duration = min(max(sleep_seconds, 1), 60)
        
        await asyncio.sleep(sleep_duration)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        return {
            "message": f"Custom sleep of {sleep_duration} seconds completed",
            "requested_sleep_seconds": sleep_duration,
            "actual_duration_seconds": round(actual_duration, 2),
            "random_number": random.randint(1, 1000),
            "timestamp": end_time
        }
