#!/usr/bin/env python3
"""
Heavy load testing script to simulate DDOS-like conditions.
This script tests how FastAPI handles many concurrent requests.
"""

import asyncio
import aiohttp
import time
import statistics
from typing import List, Dict, Any
import argparse


class LoadTester:
    def __init__(self, base_url: str = "http://localhost:8769"):
        self.base_url = base_url
        self.session = None

    async def __aenter__(self):
        # Configure session with connection limits
        connector = aiohttp.TCPConnector(
            limit=1000,  # Total connection pool size
            limit_per_host=100,  # Per-host connection limit
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=60)  # 60 second timeout
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def make_request(self, endpoint: str, request_id: int) -> Dict[str, Any]:
        """Make a single request."""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            async with self.session.get(url) as response:
                # Read response
                if response.content_type == 'application/json':
                    data = await response.json()
                else:
                    data = await response.text()
                
                end_time = time.time()
                
                return {
                    "request_id": request_id,
                    "status_code": response.status,
                    "response_time": end_time - start_time,
                    "success": response.status == 200,
                    "timestamp": end_time,
                    "data_size": len(str(data))
                }
        except asyncio.TimeoutError:
            return {
                "request_id": request_id,
                "status_code": None,
                "response_time": time.time() - start_time,
                "success": False,
                "error": "Timeout",
                "timestamp": time.time()
            }
        except Exception as e:
            return {
                "request_id": request_id,
                "status_code": None,
                "response_time": time.time() - start_time,
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }

    async def run_load_test(self, endpoint: str, num_requests: int, concurrent_limit: int = 100):
        """Run a load test with specified parameters."""
        print(f"🚀 Starting load test:")
        print(f"   Endpoint: {endpoint}")
        print(f"   Total requests: {num_requests}")
        print(f"   Concurrent limit: {concurrent_limit}")
        print(f"   Target: {self.base_url}{endpoint}")
        print()
        
        start_time = time.time()
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def limited_request(request_id):
            async with semaphore:
                return await self.make_request(endpoint, request_id)
        
        # Create all tasks
        tasks = [limited_request(i) for i in range(num_requests)]
        
        # Execute with progress reporting
        results = []
        completed = 0
        
        for coro in asyncio.as_completed(tasks):
            result = await coro
            results.append(result)
            completed += 1
            
            # Progress reporting
            if completed % max(1, num_requests // 10) == 0:
                progress = (completed / num_requests) * 100
                elapsed = time.time() - start_time
                print(f"Progress: {completed}/{num_requests} ({progress:.1f}%) - {elapsed:.1f}s elapsed")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return results, total_time

    def analyze_results(self, results: List[Dict], total_time: float):
        """Analyze and print test results."""
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]
        
        print(f"\n📊 Load Test Results:")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Total requests: {len(results)}")
        print(f"   Successful: {len(successful)} ({len(successful)/len(results)*100:.1f}%)")
        print(f"   Failed: {len(failed)} ({len(failed)/len(results)*100:.1f}%)")
        print(f"   Requests/second: {len(results)/total_time:.2f}")
        
        if successful:
            response_times = [r["response_time"] for r in successful]
            print(f"\n⏱️  Response Times (successful requests):")
            print(f"   Average: {statistics.mean(response_times):.3f}s")
            print(f"   Median: {statistics.median(response_times):.3f}s")
            print(f"   Min: {min(response_times):.3f}s")
            print(f"   Max: {max(response_times):.3f}s")
            print(f"   95th percentile: {statistics.quantiles(response_times, n=20)[18]:.3f}s")
        
        if failed:
            print(f"\n❌ Failed Requests:")
            error_types = {}
            for r in failed:
                error = r.get("error", "Unknown")
                error_types[error] = error_types.get(error, 0) + 1
            
            for error, count in error_types.items():
                print(f"   {error}: {count}")
        
        # Performance assessment
        print(f"\n🎯 Performance Assessment:")
        success_rate = len(successful) / len(results)
        avg_response_time = statistics.mean([r["response_time"] for r in successful]) if successful else float('inf')
        
        if success_rate >= 0.99 and avg_response_time < 1.0:
            print("   ✅ EXCELLENT: High success rate, fast responses")
        elif success_rate >= 0.95 and avg_response_time < 2.0:
            print("   ✅ GOOD: Good success rate, acceptable responses")
        elif success_rate >= 0.90:
            print("   ⚠️  FAIR: Decent success rate, may need optimization")
        else:
            print("   ❌ POOR: Low success rate, needs rate limiting or optimization")


async def main():
    parser = argparse.ArgumentParser(description="FastAPI Load Testing Tool")
    parser.add_argument("--requests", "-r", type=int, default=100, help="Number of requests (default: 100)")
    parser.add_argument("--concurrent", "-c", type=int, default=50, help="Concurrent requests (default: 50)")
    parser.add_argument("--endpoint", "-e", default="/async-test/load-test", help="Endpoint to test")
    parser.add_argument("--url", "-u", default="http://localhost:8769", help="Base URL")
    
    args = parser.parse_args()
    
    print("🔥 FastAPI DDOS-Style Load Testing")
    print("This will simulate heavy concurrent load on your FastAPI server")
    print("Make sure your server is running and ready!\n")
    
    async with LoadTester(args.url) as tester:
        # Test server availability first
        try:
            health_result = await tester.make_request("/async-test/health", 0)
            if not health_result["success"]:
                print(f"❌ Server not available at {args.url}")
                return
            print("✅ Server is available\n")
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            return
        
        # Run the load test
        results, total_time = await tester.run_load_test(
            args.endpoint, 
            args.requests, 
            args.concurrent
        )
        
        # Analyze results
        tester.analyze_results(results, total_time)
        
        print(f"\n🎉 Load test completed!")


def run_predefined_tests():
    """Run a series of predefined load tests."""
    tests = [
        {"name": "Light Load", "requests": 50, "concurrent": 10},
        {"name": "Medium Load", "requests": 200, "concurrent": 50},
        {"name": "Heavy Load", "requests": 500, "concurrent": 100},
        {"name": "DDOS Simulation", "requests": 1000, "concurrent": 200},
    ]
    
    async def run_tests():
        async with LoadTester() as tester:
            # Check server
            health_result = await tester.make_request("/async-test/health", 0)
            if not health_result["success"]:
                print("❌ Server not available")
                return
            
            for test in tests:
                print(f"\n{'='*60}")
                print(f"Running {test['name']} Test")
                print(f"{'='*60}")
                
                results, total_time = await tester.run_load_test(
                    "/async-test/load-test",
                    test["requests"],
                    test["concurrent"]
                )
                
                tester.analyze_results(results, total_time)
                
                # Wait between tests
                print("\nWaiting 5 seconds before next test...")
                await asyncio.sleep(5)
    
    asyncio.run(run_tests())


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "predefined":
        run_predefined_tests()
    else:
        asyncio.run(main())
