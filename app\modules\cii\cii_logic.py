from typing import Optional

from fastapi import Body

from app.modules.cii.logic_helpers.cii import Cii
from .cii_service import CiiService
from app.context.utils import rename_vat


class CiiLogic:
    def __init__(self, cii_service: CiiService, cii: Cii):
        self.cii_service = cii_service
        self.cii = cii

    async def read(self, vessel_imo: int, owner_vat: str, year: int):
        return await self.cii_service.get_cii_data(rename_vat(**locals()))

    async def years(self, vessel_imo: int, owner_vat: str):
        return await self.cii_service.get_cii_years(rename_vat(**locals()))

    async def reset(
            self,
            vessel_imo: int,
            owner_vat: str,
            year: int,
            week_numb: Optional[int] = None,
            all: Optional[str] = None,
    ):
        """
        Mirrors the old Flask behaviour:

        • /cii/reset?...&week_numb=N   -> reset one week
        • /cii/reset/all?...           -> reset the whole year
        """
        payload = {
            "vessel_imo": vessel_imo,
            "owner_vat": owner_vat,
            "year": year,
        }

        if all is None:
            # single‑week reset
            if week_numb is None:
                raise ValueError(
                    "Missing required query parameter 'week_numb' for single‑week reset"
                )
            payload["week_numb"] = week_numb

        return await self.cii_service.reset_cii(
            rename_vat(payload)
        )

    async def update(self, updated_values: dict = Body(...)):
        return await self.cii.update(updated_values)
