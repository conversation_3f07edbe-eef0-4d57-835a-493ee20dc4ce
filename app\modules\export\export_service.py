from app.services.redis_api.redis_api_client import RedisApi


class ExportService:
    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def cylinder_to(self, request_data):
        return await self.redis.call(
            path="cylinder_to",
            key="cylinderTo",
            method="POST",
            json=request_data
        )

    async def cylinder_from(self, request_data):
        return await self.redis.call(
            path="cylinder_from",
            key="cylinderFrom",
            method="POST",
            json=request_data
        )

