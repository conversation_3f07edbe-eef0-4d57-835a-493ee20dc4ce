#!/usr/bin/env python3
"""
Test script for concurrent async requests.
This script tests that FastAPI handles concurrent requests properly.
"""

import asyncio
import aiohttp
import time
from typing import List, Dict, Any


class AsyncTester:
    def __init__(self, base_url: str = "http://localhost:8769"):
        self.base_url = base_url
        self.session = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def make_request(self, endpoint: str, request_id: int = None) -> Dict[str, Any]:
        """Make a single request to an endpoint."""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            async with self.session.get(url) as response:
                data = await response.json()
                end_time = time.time()
                
                return {
                    "request_id": request_id,
                    "endpoint": endpoint,
                    "status_code": response.status,
                    "response_time": round(end_time - start_time, 2),
                    "data": data,
                    "timestamp": end_time
                }
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "endpoint": endpoint,
                "status_code": None,
                "response_time": round(end_time - start_time, 2),
                "error": str(e),
                "timestamp": end_time
            }

    async def test_basic_async_functionality(self):
        """
        Test 1: Basic async functionality
        - Start long sleep endpoint (12s)
        - Immediately call quick endpoint multiple times
        - Quick endpoints should respond immediately
        """
        print("🧪 Test 1: Basic Async Functionality")
        print("Starting long sleep endpoint, then calling quick endpoint multiple times...")
        
        start_time = time.time()
        
        # Start all requests concurrently
        tasks = [
            self.make_request("/async-test/long-sleep", 1),
            self.make_request("/async-test/quick", 2),
            self.make_request("/async-test/quick", 3),
            self.make_request("/async-test/quick", 4),
            self.make_request("/async-test/quick", 5),
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Sort results by completion time
        results.sort(key=lambda x: x["timestamp"])
        
        print(f"\nResults (completed in order):")
        for result in results:
            if "error" in result:
                print(f"❌ Request {result['request_id']} ({result['endpoint']}): ERROR - {result['error']}")
            else:
                print(f"✅ Request {result['request_id']} ({result['endpoint']}): {result['response_time']}s")
        
        # Check if quick requests completed before long sleep
        quick_requests = [r for r in results if "quick" in r["endpoint"]]
        long_requests = [r for r in results if "long-sleep" in r["endpoint"]]
        
        if quick_requests and long_requests:
            quick_max_time = max(r["response_time"] for r in quick_requests)
            long_min_time = min(r["response_time"] for r in long_requests)
            
            if quick_max_time < 2 and long_min_time > 10:
                print("✅ PASS: Quick requests completed immediately while long request was still running")
            else:
                print("❌ FAIL: Async functionality may not be working correctly")
        
        print(f"Total test time: {time.time() - start_time:.2f}s\n")

    async def test_multiple_sleep_durations(self):
        """
        Test 2: Multiple sleep durations
        - Start very long sleep (25s)
        - Start medium sleep (10s) 
        - Medium sleep should complete at least twice before very long sleep
        """
        print("🧪 Test 2: Multiple Sleep Durations")
        print("Starting very long sleep (25s), then medium sleep (10s) twice...")
        
        start_time = time.time()
        
        # Start very long sleep first
        very_long_task = asyncio.create_task(self.make_request("/async-test/very-long-sleep", 1))
        
        # Wait a moment, then start medium sleep requests
        await asyncio.sleep(1)
        
        medium_task1 = asyncio.create_task(self.make_request("/async-test/medium-sleep", 2))
        medium_task2 = asyncio.create_task(self.make_request("/async-test/medium-sleep", 3))
        
        # Wait for all to complete
        results = await asyncio.gather(very_long_task, medium_task1, medium_task2)
        
        # Sort by completion time
        results.sort(key=lambda x: x["timestamp"])
        
        print(f"\nResults (completed in order):")
        for result in results:
            if "error" in result:
                print(f"❌ Request {result['request_id']} ({result['endpoint']}): ERROR - {result['error']}")
            else:
                print(f"✅ Request {result['request_id']} ({result['endpoint']}): {result['response_time']}s")
        
        # Check if medium requests completed before very long
        medium_results = [r for r in results if "medium-sleep" in r["endpoint"]]
        very_long_results = [r for r in results if "very-long-sleep" in r["endpoint"]]
        
        if len(medium_results) == 2 and len(very_long_results) == 1:
            if all(r["timestamp"] < very_long_results[0]["timestamp"] for r in medium_results):
                print("✅ PASS: Both medium sleep requests completed before very long sleep")
            else:
                print("❌ FAIL: Medium sleep requests did not complete before very long sleep")
        
        print(f"Total test time: {time.time() - start_time:.2f}s\n")

    async def test_load_testing(self, num_requests: int = 20):
        """
        Test 3: Load testing
        - Send many concurrent requests to load test endpoint
        - Check response times and success rates
        """
        print(f"🧪 Test 3: Load Testing ({num_requests} concurrent requests)")
        print("Sending multiple concurrent requests to load test endpoint...")
        
        start_time = time.time()
        
        # Create many concurrent requests
        tasks = [
            self.make_request("/async-test/load-test", i+1) 
            for i in range(num_requests)
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Analyze results
        successful_requests = [r for r in results if "error" not in r and r["status_code"] == 200]
        failed_requests = [r for r in results if "error" in r or r["status_code"] != 200]
        
        if successful_requests:
            response_times = [r["response_time"] for r in successful_requests]
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = max_response_time = min_response_time = 0
        
        print(f"\nLoad Test Results:")
        print(f"✅ Successful requests: {len(successful_requests)}/{num_requests}")
        print(f"❌ Failed requests: {len(failed_requests)}")
        print(f"📊 Response times - Avg: {avg_response_time:.2f}s, Min: {min_response_time:.2f}s, Max: {max_response_time:.2f}s")
        
        if len(successful_requests) == num_requests:
            print("✅ PASS: All requests completed successfully")
        elif len(successful_requests) >= num_requests * 0.9:
            print("⚠️  PARTIAL: Most requests completed successfully")
        else:
            print("❌ FAIL: Many requests failed - may need rate limiting")
        
        print(f"Total test time: {time.time() - start_time:.2f}s\n")


async def main():
    """Run all async tests."""
    print("🚀 Starting FastAPI Async Functionality Tests")
    print("Make sure your FastAPI server is running on http://localhost:8769\n")
    
    async with AsyncTester() as tester:
        # Test server availability
        try:
            health_result = await tester.make_request("/async-test/health")
            if health_result.get("status_code") != 200:
                print("❌ Server not available. Make sure FastAPI is running on http://localhost:8769")
                return
            print("✅ Server is available\n")
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            return
        
        # Run all tests
        await tester.test_basic_async_functionality()
        await tester.test_multiple_sleep_durations()
        await tester.test_load_testing(20)
        
        print("🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
